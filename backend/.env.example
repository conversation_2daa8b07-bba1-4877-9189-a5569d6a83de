# DonorLink Backend Environment Configuration

# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=life

# JWT Configuration (for future authentication features)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# CORS Configuration (optional)
CORS_ORIGIN=*

# API Configuration
API_TIMEOUT=10000

# Logging
LOG_LEVEL=info
